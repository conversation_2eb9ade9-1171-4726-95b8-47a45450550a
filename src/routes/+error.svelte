<script>
  import { page } from "$app/state";
</script>

<div class="error-container">
  {#if page.status}
    <h1 class="status-code">{page.status}</h1>
  {/if}

  {#if page.error?.message}
    <p class="error-message">{page.error.message}</p>
  {:else}
    <p class="error-message">Something went wrong.</p>
  {/if}
</div>

<style>
  .error-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    animation: fadeIn 1s ease-out forwards;
    padding: var(--padding-xl);
    min-height: 80vh;
    color: var(--gray-950);
  }

  .status-code {
    color: var(--gray-400);
    font-size: clamp(4rem, 20vw, 8rem);
  }

  .error-message {
    color: var(--gray-700);
    font-size: var(--font-md);
  }
</style>
