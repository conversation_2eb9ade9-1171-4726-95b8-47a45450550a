import { logger } from "$lib/logger/logger.svelte.js";
import { getRestaurantInfo } from "$lib/server/get-restaurant-info.ts";
import { error, isHttpError } from "@sveltejs/kit";
import fs from "node:fs/promises";
import path from "node:path";
import { checkBot, BotDetectedError } from "$lib/helpers/security";

export const load = async (event) => {
  const { restaurant } = event.params;

  try {
    // Check for bot detection first
    await checkBot(event);
    const restaurantInfo = await getRestaurantInfo(event.fetch, restaurant);

    if (restaurantInfo.items.length === 0) {
      logger.error({ restaurant, restaurantInfo }, "Restaurant info is empty");
      error(503, {
        message:
          "Our servers are temporarily unable to process your request. Please try again later.",
      });
    }

    // TODO This will be dynamic per merchant in the future
    const BANNER_IMAGE_FILENAME = "merchant_banner.png";
    const imageFilePath = path.join(
      process.cwd(),
      "static",
      BANNER_IMAGE_FILENAME,
    );

    let bannerImageExists = false;
    try {
      await fs.access(imageFilePath);
      bannerImageExists = true;
    } catch {
      bannerImageExists = false;
    }

    return {
      restaurantInfo,
      bannerImageExists,
      bannerImageUrl: `/${BANNER_IMAGE_FILENAME}`,
    };
  } catch (err) {
    if (err instanceof BotDetectedError) {
      error(403, "Forbidden");
    }

    logger.error(err, "Error loading restaurant info");
    if (isHttpError(err)) {
      error(err.status, err.body);
    } else {
      error(500, err instanceof Error ? err.message : "Unknown error");
    }
  }
};
