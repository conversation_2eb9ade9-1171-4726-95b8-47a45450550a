<script lang="ts">
  import {
    BridgeClient,
    setBridgeClient,
  } from "$lib/client/bridge/bridge-client.js";
  import { MockBridgeClient } from "$lib/client/bridge/bridge-client.mock.js";
  import { setCartState } from "$lib/services/Cart.svelte.js";
  import { setStoreInfoState } from "$lib/services/StoreInfo.svelte";
  import { initHeaderState } from "$lib/services/TopHeader.svelte.js";
  import Header from "$lib/components/Header.svelte";
  import { setHealthCheckService } from "$lib/services/HealthCheckService.svelte.js";
  import { publicConfig } from "../../publicConfig.js";
  // import { checkBot } from "$lib/helpers/security";

  // checkBot();

  const { data, children } = $props();
  const { restaurantInfo } = data;

  const bridgeClient =
    publicConfig.env === "dev"
      ? new MockBridgeClient(fetch, restaurantInfo.device_id)
      : new BridgeClient(fetch, restaurantInfo.device_id);

  setStoreInfoState(restaurantInfo.storeInfo);
  initHeaderState();
  setCartState();

  setBridgeClient(bridgeClient);
  const healthCheckService = setHealthCheckService(bridgeClient);
  healthCheckService.startPolling();
</script>

<Header
  bannerImageUrl={data.bannerImageExists ? data.bannerImageUrl : undefined}
/>

{@render children()}
