import type { BasicDepartment, BasicItem } from "$lib/types";

export const storeInfo = {
  name: "Cheesy Slice Pizza",
  address: "123 Cheesy Street",
  city: "Cheese Loversville",
  state: "CL",
  phone: "************",
  onlineActive: true,
  storeHours: {
    mon: [
      {
        open: 32400000, // 9:00 AM
        close: 64800000, // 18:00 PM
      },
    ],
    tue: [
      {
        open: 32400000, // 9:00 AM
        close: 64800000, // 18:00 PM
      },
    ],
    wed: [
      {
        open: 32400000, // 9:00 AM
        close: 64800000, // 18:00 PM
      },
    ],
    thu: [
      {
        open: 32400000, // 9:00 AM
        close: 64800000, // 18:00 PM
      },
    ],
    fri: [
      {
        open: 32400000, // 9:00 AM
        close: 64800000, // 18:00 PM
      },
    ],
    sat: [
      {
        open: 32400000, // 9:00 AM
        close: 64800000, // 18:00 PM
      },
    ],
    sun: [
      {
        open: 32400000, // 9:00 AM
        close: 64800000, // 18:00 PM
      },
    ],
    xmas: [],
    xmasEve: [],
    nwYrs: [],
    nwYrsEve: [],
    thanks: [],
    ind: [],
    labor: [],
    memor: [],
    colum: [],
    vets: [],
    pres: [],
    mlk: [],
  },
};

export const departments: BasicDepartment[] = [
  {
    department: "1",
    title: "Appetizers",
  },
  {
    department: "2",
    title: "Pizzas",
  },
  {
    department: "3",
    title: "Main Courses",
  },
  {
    department: "4",
    title: "Desserts",
  },
  {
    department: "5",
    title: "Beverages",
  },
  {
    department: "6",
    title: "Salads",
  },
  {
    department: "7",
    title: "Sandwiches",
  },
  {
    department: "8",
    title: "Soups",
  },
  {
    department: "9",
    title: "Seafood",
  },
];

export const items: BasicItem[] = [
  {
    item: "12382",
    price: 999,
    desc: "Double Multimodlist",
    detailedDesc: "Item with required children labels",
    department: "1",
    count: 20,
  },
  {
    item: "12345",
    price: 999,
    desc: "Burger",
    detailedDesc: "Burger",
    department: "7",
    count: 20,
  },
  {
    item: "12346",
    price: 1299,
    desc: "Classic pizza",
    detailedDesc: "A classic pizza with your choice of toppings.",
    department: "2",
    count: 15,
  },
  {
    item: "12347",
    price: 1599,
    desc: "Grilled chicken breast",
    detailedDesc: "Grilled chicken breast served with a side of vegetables.",
    department: "3",
    count: 12,
  },
  {
    item: "12348",
    price: 899,
    desc: "Caesar Salad",
    detailedDesc:
      "Crisp romaine lettuce tossed in Caesar dressing with croutons.",
    department: "6",
    count: 25,
  },
  {
    item: "12349",
    price: 1199,
    desc: "Beer-battered fish fillet",
    detailedDesc:
      "Beer-battered fish fillet served with tartar sauce and lemon.",
    department: "9",
    count: 8,
  },
  {
    item: "12350",
    price: 1999,
    desc: "Grilled steak",
    detailedDesc: "Juicy grilled steak cooked to your preference.",
    department: "3",
    count: 10,
  },
  {
    item: "12351",
    price: 0,
    desc: "Item with no modifiers",
    detailedDesc: "Item with no modifiers",
    department: "1",
    count: 30,
  },
  {
    item: "12352",
    price: 999,
    desc: "Cheeseburger",
    detailedDesc:
      "Juicy beef patty topped with melted cheese, served with fries.",
    department: "7",
    count: 18,
  },
  {
    item: "12353",
    price: 1299,
    desc: "Margherita Pizza",
    detailedDesc:
      "Classic pizza topped with fresh mozzarella, tomatoes, and basil.",
    department: "2",
    count: 14,
  },
  {
    item: "12354",
    price: 1999,
    desc: "Steak Frites",
    detailedDesc:
      "Grilled steak served with French fries and a side of herb butter.",
    department: "3",
    count: 7,
  },
  {
    item: "12344",
    price: 1500,
    desc: "Chicken Alfredo",
    detailedDesc: "Creamy Alfredo sauce with grilled chicken over pasta.",
    department: "3",
    count: 9,
  },
  {
    item: "12355",
    price: 25,
    desc: "Out of stock item",
    detailedDesc: "Out of stock item",
    department: "1",
    count: 0,
  },
  {
    // simplified replica of Monroe's 'Double LIQ' item
    item: "43a223cf-d21d-4080-8721-362dd96ae562",
    price: 0,
    desc: "Deeply nested item with no labels",
    detailedDesc: "Deeply nested item with no labels",
    department: "3",
    count: 1,
  },
  // New items for departments without items
  {
    item: "12356",
    price: 799,
    desc: "Chocolate Cake",
    detailedDesc: "Rich and decadent chocolate cake.",
    department: "4", // Desserts
    count: 22,
  },
  {
    item: "12357",
    price: 699,
    desc: "Tiramisu",
    detailedDesc: "Classic Italian dessert with coffee-soaked ladyfingers.",
    department: "4", // Desserts
    count: 16,
  },
  {
    item: "12358",
    price: 899,
    desc: "Cheesecake",
    detailedDesc:
      "Creamy New York style cheesecake with a graham cracker crust.",
    department: "4", // Desserts
    count: 19,
  },
  {
    item: "12359",
    price: 299,
    desc: "Soda",
    detailedDesc: "Your choice of refreshing soda.",
    department: "5", // Beverages
    count: 50,
  },
  {
    item: "12360",
    price: 399,
    desc: "Iced Tea",
    detailedDesc: "Freshly brewed iced tea, sweetened or unsweetened.",
    department: "5", // Beverages
    count: 45,
  },
  {
    item: "12361",
    price: 499,
    desc: "Coffee",
    detailedDesc: "Hot brewed coffee.",
    department: "5", // Beverages
    count: 40,
  },
  {
    item: "12362",
    price: 699,
    desc: "Tomato Soup",
    detailedDesc: "Creamy tomato soup, perfect for a chilly day.",
    department: "8", // Soups
    count: 25,
  },
  {
    item: "12363",
    price: 799,
    desc: "Chicken Noodle Soup",
    detailedDesc: "Hearty chicken noodle soup with vegetables.",
    department: "8", // Soups
    count: 20,
  },
  {
    item: "12364",
    price: 899,
    desc: "Clam Chowder",
    detailedDesc: "New England style clam chowder.",
    department: "8", // Soups
    count: 15,
  },
  // Additional items for existing departments
  {
    item: "12365",
    price: 1099,
    desc: "Mozzarella Sticks",
    detailedDesc: "Crispy fried mozzarella sticks served with marinara sauce.",
    department: "1", // Appetizers
    count: 28,
  },
  {
    item: "12366",
    price: 1399,
    desc: "Pepperoni Pizza",
    detailedDesc: "Classic pepperoni pizza.",
    department: "2", // Pizzas
    count: 17,
  },
  {
    item: "12367",
    price: 1499,
    desc: "Vegetarian Pizza",
    detailedDesc: "Pizza topped with a variety of fresh vegetables.",
    department: "2", // Pizzas
    count: 12,
  },
  {
    item: "12368",
    price: 1799,
    desc: "Ribeye Steak",
    detailedDesc: "A tender and flavorful ribeye steak.",
    department: "3", // Main Courses
    count: 1,
  },
  {
    item: "12369",
    price: 999,
    desc: "Greek Salad",
    detailedDesc: "Salad with feta cheese, olives, tomatoes, and cucumbers.",
    department: "6", // Salads
    count: 22,
  },
  {
    item: "12370",
    price: 1099,
    desc: "Club Sandwich",
    detailedDesc:
      "Triple-decker sandwich with turkey, bacon, lettuce, and tomato.",
    department: "7", // Sandwiches
    count: 24,
  },
  {
    item: "12371",
    price: 1499,
    desc: "Grilled Salmon",
    detailedDesc: "Perfectly grilled salmon fillet.",
    department: "9", // Seafood
    count: 10,
  },
  {
    item: "12372",
    price: 1699,
    desc: "Lobster Tail",
    detailedDesc: "Succulent broiled lobster tail.",
    department: "9", // Seafood
    count: 5,
  },
  {
    item: "6fae7d04-d546-4d99-90f7-27b4e08b3857",
    price: 0,
    desc: "Monday Lunch",
    detailedDesc: "Monday Lunch",
    department: "3",
    count: 1,
  },
];
