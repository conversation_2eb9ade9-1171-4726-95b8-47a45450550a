import { error } from "@sveltejs/kit";
import { load } from "@fingerprintjs/botd";
import type { RequestEvent } from "@sveltejs/kit";

// Client-side bot detection using fingerprintjs
export const checkBotClient = async () => {
  try {
    const botd = await load();
    const { bot } = botd.detect();

    console.log("Bot detected on client:", bot);

    if (!bot) {
      throw error(403, "Forbidden");
    }
    return bot;
  } catch (err) {
    console.error(err);
  }
};

// Custom error class for bot detection
export class BotDetectedError extends Error {
  constructor() {
    super("Bot detected");
    this.name = "BotDetectedError";
  }
}

// Server-side bot detection using User-Agent analysis
export const checkBotServer = (event: RequestEvent) => {
  const userAgent = event.request.headers.get("user-agent") || "";

  // Common bot patterns in User-Agent strings
  const botPatterns = [
    /bot/i,
    /crawler/i,
    /spider/i,
    /scraper/i,
    /curl/i,
    /wget/i,
    /python/i,
    /java/i,
    /go-http-client/i,
    /postman/i,
    /insomnia/i,
    /httpie/i,
  ];

  const isBot = botPatterns.some(pattern => pattern.test(userAgent));

  if (isBot) {
    throw new BotDetectedError();
  }

  return false;
};

// Unified function that works in both environments
export const checkBot = async (event?: RequestEvent) => {
  // If we have a request event, we're on the server
  if (event) {
    return checkBotServer(event);
  }

  // Otherwise, we're on the client
  return checkBotClient();
};
