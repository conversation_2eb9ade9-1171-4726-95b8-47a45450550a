import { fetch } from "node-fetch";

async function testBotDetection() {
  try {
    console.log("Testing bot detection with curl User-Agent...");

    const response = await fetch("http://localhost:5174/restaurant-1", {
      headers: {
        "User-Agent": "curl/7.68.0",
      },
    });

    console.log("Status:", response.status);
    console.log("Status Text:", response.statusText);

    const text = await response.text();
    console.log("Response body:", text.substring(0, 500) + "...");
  } catch (error) {
    console.error("Error:", error.message);
  }
}

testBotDetection();
