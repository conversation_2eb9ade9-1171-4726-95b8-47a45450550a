import { centToDollar } from "../src/lib/helpers/number.ts";
import { expect, test } from "@playwright/test";
import { config } from "./global";
import {
  addWithModifier,
  checkItemInCart,
  findCartItem,
  getMoreSelectionsButton,
  openItemDialog,
  selectItemAndAddToCart,
  selectModifiers,
  updateSelectedModifiersInCart,
} from "./items";

test.describe("Item modifiers and cart functionality", () => {
  test.beforeEach(async ({ page }) => {
    await page.goto(config("1").baseUrl);
  });

  // https://playwright.dev/docs/test-parallel
  test.describe.configure({ mode: "parallel" });

  test("selecting max modifiers disables remaining", async ({ page }) => {
    const itemName = "Classic pizza";
    const dialog = await openItemDialog(page, itemName);
    const modifiersToSelect = [
      "Medium",
      "Thin Crust",
      "Pepperoni",
      "Extra Cheese",
    ];

    await selectModifiers({
      locator: dialog,
      modifiersToSelect,
    });
    const checkedCheckboxes = await dialog
      .locator('input[type="checkbox"]:checked')
      .count();
    const disabledCheckboxes = await dialog
      .locator('input[type="checkbox"]:disabled')
      .count();

    expect(checkedCheckboxes).toBe(4);
    expect(disabledCheckboxes).toBe(6);

    await expect(
      dialog.getByRole("checkbox", { name: "Olives" }),
    ).toBeDisabled();
  });

  test("should handle item quantity changes", async ({ page, isMobile }) => {
    const dialog = await openItemDialog(page, "Caesar Salad");

    const increaseQuantityButton = dialog.getByRole("button", {
      name: "Increase quantity by 1",
    });

    await increaseQuantityButton.click();
    await increaseQuantityButton.click();

    await dialog.getByRole("button", { name: "Add to cart" }).click();

    const caesarSalad = await findCartItem(page, "Caesar Salad", isMobile);

    await expect(caesarSalad.locator(".item-quantity")).toContainText("3×");

    const itemPrice = 899;
    const expectedTotal = itemPrice * 3; //2697
    const formattedTotal = centToDollar(expectedTotal);

    const totalElement = page
      .getByRole("region", { name: "Cart Footer" })
      .locator(".total-amount");

    if (formattedTotal) {
      await expect(totalElement).toContainText(formattedTotal);
    }
  });

  // TODO: This WILL FAIL in mobile view because the modal is inaccessible
  test("should correctly reset label modifiers when adding to cart", async ({
    page,
    isMobile,
  }) => {
    await addWithModifier({
      page,
      itemName: "Beer-battered fish fillet",
      modifiersToSelect: ["Tartar Sauce", "Chips"],
    });

    const cartItem = await findCartItem(
      page,
      "Beer-battered fish fillet",
      isMobile,
    );

    await expect(cartItem.getByText("Tartar Sauce")).toBeVisible();
    await expect(cartItem.getByText("Side Options")).toBeVisible();
    await expect(cartItem.getByText("Chips")).toBeVisible();

    await updateSelectedModifiersInCart(
      page,
      "Beer-battered fish fillet",
      ["Tartar Sauce"],
      isMobile,
    );

    await expect(cartItem.getByText("Tartar Sauce")).toBeVisible();
    await expect(cartItem.getByText("Side Options")).not.toBeVisible();
  });

  test("should handle remove item from cart", async ({ page, isMobile }) => {
    await selectItemAndAddToCart(page, "Grilled steak");

    const cartItem = await findCartItem(page, "Grilled steak", isMobile);
    await expect(cartItem).toBeVisible();

    await cartItem.getByRole("button", { name: "Remove" }).click();
    await expect(cartItem).not.toBeVisible();
  });

  test("should be able to edit item in cart", async ({ page, isMobile }) => {
    await addWithModifier({
      page,
      itemName: "Cheeseburger",
      modifiersToSelect: [
        "American Cheese",
        "Sesame Seed Bun",
        "Special Sauce",
        "Beef Patty",
        "French Fries",
        "Medium",
      ],
    });

    await checkItemInCart({
      page,
      itemName: "Cheeseburger",
      modifiers: [
        "American Cheese",
        "Sesame Seed Bun",
        "Special Sauce",
        "Beef Patty",
        "French Fries",
        "Medium",
      ],
      isMobile,
    });

    // TODO: Will break here in mobile view because the modal is inaccessible
    await updateSelectedModifiersInCart(
      page,
      "Cheeseburger",
      [
        "Swiss Cheese",
        "Brioche Bun",
        "Special Sauce",
        "Veggie Patty",
        "Onion Rings",
        "Well Done",
      ],
      isMobile,
    );

    await checkItemInCart({
      page,
      itemName: "Cheeseburger",
      modifiers: [
        "Swiss Cheese",
        "Brioche Bun",
        "Special Sauce",
        "Veggie Patty",
        "Onion Rings",
        "Well Done",
      ],
      isMobile,
    });
  });

  test("Add to cart button is disabled when required modifiers are not selected", async ({
    page,
  }) => {
    const item = "Classic pizza";
    const dialog = await openItemDialog(page, item);
    await selectModifiers({ locator: dialog, modifiersToSelect: ["Medium"] });

    await expect(
      dialog.getByRole("button", { name: "Add to cart" }),
    ).toBeDisabled();
  });

  test("Triple nested required modifiers are handled correctly", async ({
    page,
    isMobile,
  }) => {
    const itemName = "Cheeseburger";
    const modifiersToSelect = [
      "American Cheese",
      "Sesame Seed Bun",
      "Beef Patty",
      "Onion Rings",
      "Medium",
      "Drink",
    ];

    const dialog = await openItemDialog(page, itemName);

    await selectModifiers({
      locator: dialog,
      modifiersToSelect,
    });

    await getMoreSelectionsButton(dialog).click();

    const drinkRegion = dialog.getByRole("region", { name: "Drink" });

    await selectModifiers({
      locator: drinkRegion,
      modifiersToSelect: ["Soda"],
    });
    await getMoreSelectionsButton(drinkRegion).click();

    const sodaRegion = dialog.getByRole("region", { name: "Soda" });
    await selectModifiers({
      locator: sodaRegion,
      modifiersToSelect: ["Pepsi"],
    });
    await sodaRegion.getByRole("button", { name: "Go Back" }).click();
    await drinkRegion.getByRole("button", { name: "Go Back" }).click();

    await expect(
      dialog.getByRole("button", { name: "Add to cart" }),
    ).not.toBeDisabled();
    await dialog.getByRole("button", { name: "Add to cart" }).click();

    await checkItemInCart({
      page,
      itemName,
      modifiers: [...modifiersToSelect, "Soda", "Pepsi"],
      isMobile,
    });
  });

  test("Item with no labels is validated correctly", async ({
    page,
    isMobile,
  }) => {
    const itemName = "Deeply nested item with no labels";
    const dialog = await openItemDialog(page, itemName);
    await selectModifiers({
      locator: dialog,
      modifiersToSelect: ["Doub Captn"],
    });

    await expect(
      dialog.getByRole("button", { name: "Add to cart" }),
    ).toBeDisabled();

    await getMoreSelectionsButton(dialog).click();

    const doubleCaptainRegion = dialog.getByRole("region", {
      name: "Doub Captn",
    });

    await selectModifiers({
      locator: doubleCaptainRegion,
      modifiersToSelect: ["Rocks", "Tall Gls"],
    });

    await doubleCaptainRegion.getByRole("button", { name: "Go Back" }).click();

    await expect(
      dialog.getByRole("button", { name: "Add to cart" }),
    ).not.toBeDisabled();

    await dialog.getByRole("button", { name: "Add to cart" }).click();

    await checkItemInCart({
      page,
      itemName,
      modifiers: ["Rocks", "Tall Gls"],
      isMobile,
    });
  });

  test("Item with dynamic pricing should be validated correctly", async ({
    page,
  }) => {
    const itemName = "Deeply nested item with no labels";
    const dialog = await openItemDialog(page, itemName);

    const addToCartButton = dialog.getByRole("button", { name: "Add to cart" });
    await expect(addToCartButton).toBeDisabled();

    await selectModifiers({
      locator: dialog,
      modifiersToSelect: ["Doub Captn"],
    });

    await getMoreSelectionsButton(dialog).click();

    const doubleCaptainRegion = dialog.getByRole("region", {
      name: "Doub Captn",
    });

    await selectModifiers({
      locator: doubleCaptainRegion,
      modifiersToSelect: ["Rocks", "Tall Gls"],
    });

    await doubleCaptainRegion.getByRole("button", { name: "Go Back" }).click();

    await expect(
      dialog.getByRole("button", { name: "Add to cart" }),
    ).not.toBeDisabled();
  });

  test("cart items should be selected appropriately", async ({
    page,
    isMobile,
  }) => {
    const dialog = await openItemDialog(page, "Cheeseburger");

    await selectModifiers({
      locator: dialog,
      modifiersToSelect: [
        "American Cheese",
        "Sesame Seed Bun",
        "Beef Patty",
        "Onion Rings",
        "Medium",
        "Drink",
      ],
    });

    await getMoreSelectionsButton(dialog).click();
    const drinkRegion = dialog.getByRole("region", { name: "Drink" });

    await selectModifiers({
      locator: drinkRegion,
      modifiersToSelect: ["Soda"],
    });

    await getMoreSelectionsButton(drinkRegion).click();
    const sodaRegion = dialog.getByRole("region", { name: "Soda" });

    await selectModifiers({
      locator: sodaRegion,
      modifiersToSelect: ["Pepsi"],
    });

    await sodaRegion.getByRole("button", { name: "Go Back" }).click();
    await drinkRegion.getByRole("button", { name: "Go Back" }).click();

    await expect(
      dialog.getByRole("button", { name: "Add to cart" }),
    ).not.toBeDisabled();

    await dialog.getByRole("button", { name: "Add to cart" }).click();

    const cartItem = await findCartItem(page, "Cheeseburger", isMobile);

    await expect(cartItem).toBeVisible();

    /****************** visible modifiers *******************/
    await expect(cartItem.getByText("Cheese Options")).toBeVisible();
    await expect(cartItem.getByText("American Cheese")).toBeVisible();

    await expect(cartItem.getByText("Bun Type")).toBeVisible();
    await expect(cartItem.getByText("Sesame Seed Bun")).toBeVisible();

    await expect(cartItem.getByText("Patty Options")).toBeVisible();
    await expect(cartItem.getByText("Beef Patty")).toBeVisible();

    await expect(cartItem.getByText("Side Options")).toBeVisible();
    await expect(cartItem.getByText("Onion Rings")).toBeVisible();

    await expect(cartItem.getByText("Cooking Preferences")).toBeVisible();
    await expect(cartItem.getByText("Medium")).toBeVisible();

    await expect(cartItem.getByText("Meal Add-ons")).toBeVisible();
    await expect(cartItem.getByText("Drink")).toBeVisible();
    await expect(cartItem.getByText("Soda")).toBeVisible();
    await expect(cartItem.getByText("Pepsi")).toBeVisible();

    /****************** hidden modifiers *******************/
    await expect(cartItem.getByText("Toppings")).not.toBeVisible();
    await expect(cartItem.getByText("Sauces")).not.toBeVisible();

    await updateSelectedModifiersInCart(
      page,
      "Cheeseburger",
      [
        "American Cheese",
        "Sesame Seed Bun",
        "Beef Patty",
        "Tomato",
        "Lettuce",
        "Ketchup",
        "Onion Rings",
        "Well Done",
      ],
      isMobile,
    );

    const newCartItem = await findCartItem(page, "Cheeseburger", isMobile);

    await expect(newCartItem).toBeVisible();

    /****************** visible modifiers *******************/
    await expect(newCartItem.getByText("Cheese Options")).toBeVisible();
    await expect(newCartItem.getByText("American Cheese")).toBeVisible();

    await expect(newCartItem.getByText("Bun Type")).toBeVisible();
    await expect(newCartItem.getByText("Sesame Seed Bun")).toBeVisible();

    await expect(newCartItem.getByText("Patty Options")).toBeVisible();
    await expect(newCartItem.getByText("Beef Patty")).toBeVisible();

    await expect(newCartItem.getByText("Toppings")).toBeVisible();
    await expect(newCartItem.getByText("Tomato")).toBeVisible();
    await expect(newCartItem.getByText("Lettuce")).toBeVisible();

    await expect(newCartItem.getByText("Sauces")).toBeVisible();
    await expect(newCartItem.getByText("Ketchup")).toBeVisible();

    await expect(newCartItem.getByText("Side Options")).toBeVisible();
    await expect(newCartItem.getByText("Onion Rings")).toBeVisible();

    await expect(newCartItem.getByText("Cooking Preferences")).toBeVisible();
    await expect(newCartItem.getByText("Well Done")).toBeVisible();

    /****************** hidden modifiers *******************/
    await expect(newCartItem.getByText("Meal Add-ons")).not.toBeVisible();
    await expect(newCartItem.getByText("Drink")).not.toBeVisible();
    await expect(newCartItem.getByText("Soda")).not.toBeVisible();
    await expect(newCartItem.getByText("Pepsi")).not.toBeVisible();
  });

  test("should add to item price when a priced modifier is selected", async ({
    page,
  }) => {
    const itemName = "Burger";

    const dialog = await openItemDialog(page, itemName);

    await selectModifiers({
      locator: dialog,
      modifiersToSelect: ["American Cheese", "Brioche Bun"],
    });

    const itemTotal = dialog.locator(".item-total");

    expect(await itemTotal.textContent()).toEqual("$11.98");
  });

  test("should render a modifier input as disabled if it's out of stock", async ({
    page,
  }) => {
    const dialog = await openItemDialog(page, "Caesar Salad");

    const input = dialog.getByRole("checkbox", { name: "Bacon Bits" });

    await expect(input).toBeDisabled();
  });

  test("should disable Add to Cart button if item has incomplete required selections", async ({
    page,
  }) => {
    const itemName = "Monday Lunch";

    const dialog = await openItemDialog(page, itemName);

    await expect(
      dialog.getByRole("button", { name: "Add to Cart" }),
    ).toBeDisabled();

    await page
      .getByRole("checkbox", { name: "Michaels Fish Sandwich" })
      .click();

    await expect(
      dialog.getByRole("button", { name: "Add to Cart" }),
    ).toBeDisabled();

    await getMoreSelectionsButton(page).click();

    await page.getByRole("checkbox", { name: "Cup of Soup Upcharge" }).click();

    await expect(
      dialog.getByRole("button", { name: "Add to Cart" }),
    ).not.toBeDisabled();
  });

  test("Toggling between Selections required → Back should not cause validation to flip to invalid", async ({
    page,
  }) => {
    const itemName = "Double Multimodlist";
    const dialog = await openItemDialog(page, itemName);

    await selectModifiers({
      locator: dialog,
      modifiersToSelect: ["MultiModlist True"],
    });

    await getMoreSelectionsButton(dialog).click();

    await expect(
      dialog.getByRole("button", { name: "Confirm choice" }),
    ).toBeDisabled();

    await selectModifiers({
      locator: dialog,
      modifiersToSelect: ["MultiModlist False"],
    });

    await expect(
      dialog.getByRole("button", { name: "Confirm choice" }),
    ).not.toBeDisabled();

    await dialog.getByRole("button", { name: "Go Back" }).click();

    await expect(
      dialog.getByRole("button", { name: "Add to cart" }),
    ).not.toBeDisabled();

    await getMoreSelectionsButton(dialog).click();

    await expect(
      dialog.getByRole("button", { name: "Confirm choice" }),
    ).not.toBeDisabled();
  });
});
